"""
Question Classification Service
Business logic for question classification and routing
"""

from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
import structlog
import uuid

from app.agents.question_classification import QuestionClassificationAgent
from app.agents.base import AgentConfig, AgentRole
from app.core.app_rules import success_response, error_response

logger = structlog.get_logger()


class QuestionClassificationService:
    """Service for classifying questions into answerable vs escalation categories"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self._agent = None
    
    def _get_agent(self) -> QuestionClassificationAgent:
        """Get or create the question classification agent"""
        if self._agent is None:
            config = AgentConfig(
                role=AgentRole.QUESTION_ANSWERING,
                model="gpt-4o",
                temperature=0.3,
                max_tokens=1000,
                description="Classify questions into answerable vs escalation categories"
            )
            self._agent = QuestionClassificationAgent(config)
        return self._agent
    
    async def classify_question(
        self,
        question_text: str,
        lead_id: Optional[str] = None,
        franchisor_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Classify a question and insert into appropriate table
        
        Args:
            question_text: The question to classify
            lead_id: Optional lead ID
            franchisor_id: Optional franchisor ID
            context: Optional context information (brochure text, system knowledge)
            
        Returns:
            Classification result with SQL statements
        """
        try:
            logger.info(f"Classifying question: {question_text[:100]}...")
            
            # Validate input
            if not question_text or not question_text.strip():
                return error_response(
                    title="Invalid Input",
                    description="Question text is required",
                    error_code=400
                )
            
            # Get the classification agent
            agent = self._get_agent()
            
            # Perform classification
            result = await agent.classify_question(
                question_text=question_text.strip(),
                lead_id=lead_id,
                franchisor_id=franchisor_id,
                context=context
            )
            
            if not result.get("success"):
                return error_response(
                    title="Classification Failed",
                    description=result.get("error", "Unknown error during classification"),
                    error_code=500
                )
            
            classification_result = result.get("result", {})
            rag_answer = result.get("rag_answer")
            analysis = result.get("analysis", {})
            
            # Format response based on classification
            if classification_result.get("classification") == "answerable":
                return success_response(
                    title="Question Classified as Answerable",
                    description=f"Question inserted into question_bank table",
                    data={
                        "classification": "answerable",
                        "table": "question_bank",
                        "question": question_text,
                        "answer": rag_answer,
                        "sql_statement": classification_result.get("sql_result", ""),
                        "analysis": analysis,
                        "lead_id": lead_id,
                        "franchisor_id": franchisor_id
                    }
                )
            
            elif classification_result.get("classification") == "escalation":
                return success_response(
                    title="Question Classified for Escalation",
                    description=f"Question inserted into escalation_question_bank table",
                    data={
                        "classification": "escalation",
                        "table": "escalation_question_bank",
                        "question": question_text,
                        "reason": classification_result.get("reason", "Requires human support"),
                        "sql_statement": classification_result.get("sql_result", ""),
                        "analysis": analysis,
                        "attempted_answer": rag_answer,
                        "lead_id": lead_id,
                        "franchisor_id": franchisor_id
                    }
                )
            
            else:
                return error_response(
                    title="Classification Error",
                    description=classification_result.get("error", "Unknown classification error"),
                    error_code=500
                )
                
        except Exception as e:
            logger.error(f"Error in question classification service: {str(e)}")
            return error_response(
                title="Service Error",
                description=f"An error occurred during question classification: {str(e)}",
                error_code=500
            )
    
    async def batch_classify_questions(
        self,
        questions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Classify multiple questions in batch
        
        Args:
            questions: List of question dictionaries with keys:
                - question_text: str
                - lead_id: Optional[str]
                - franchisor_id: Optional[str]
                - context: Optional[Dict[str, Any]]
                
        Returns:
            Batch classification results
        """
        try:
            results = []
            
            for i, question_data in enumerate(questions):
                try:
                    result = await self.classify_question(
                        question_text=question_data.get("question_text", ""),
                        lead_id=question_data.get("lead_id"),
                        franchisor_id=question_data.get("franchisor_id"),
                        context=question_data.get("context")
                    )
                    
                    results.append({
                        "index": i,
                        "question": question_data.get("question_text", ""),
                        "result": result
                    })
                    
                except Exception as e:
                    logger.error(f"Error classifying question {i}: {str(e)}")
                    results.append({
                        "index": i,
                        "question": question_data.get("question_text", ""),
                        "result": error_response(
                            title="Individual Classification Error",
                            description=str(e),
                            error_code=500
                        )
                    })
            
            # Count successful classifications
            successful = sum(1 for r in results if r["result"].get("success", False))
            failed = len(results) - successful
            
            return success_response(
                title="Batch Classification Complete",
                description=f"Processed {len(results)} questions: {successful} successful, {failed} failed",
                data={
                    "total_questions": len(results),
                    "successful_classifications": successful,
                    "failed_classifications": failed,
                    "results": results
                }
            )
            
        except Exception as e:
            logger.error(f"Error in batch question classification: {str(e)}")
            return error_response(
                title="Batch Classification Error",
                description=f"An error occurred during batch classification: {str(e)}",
                error_code=500
            )
    
    async def get_classification_examples(self) -> Dict[str, Any]:
        """
        Get examples of question classifications for documentation
        
        Returns:
            Examples of answerable and escalation questions
        """
        try:
            examples = {
                "answerable_questions": [
                    {
                        "question": "What's the ROI of this franchise?",
                        "reason": "Can be answered from brochure financial information",
                        "table": "question_bank"
                    },
                    {
                        "question": "What are the training requirements?",
                        "reason": "Training information available in franchise documents",
                        "table": "question_bank"
                    },
                    {
                        "question": "How much is the initial investment?",
                        "reason": "Investment details typically in franchise brochures",
                        "table": "question_bank"
                    }
                ],
                "escalation_questions": [
                    {
                        "question": "Do you sell ice cream trucks?",
                        "reason": "Not mentioned in available franchise documents",
                        "table": "escalation_question_bank"
                    },
                    {
                        "question": "What's your GST number?",
                        "reason": "Requires specific business information not in brochures",
                        "table": "escalation_question_bank"
                    },
                    {
                        "question": "Help",
                        "reason": "Too vague and unclear",
                        "table": "escalation_question_bank"
                    }
                ],
                "sql_examples": {
                    "question_bank_insert": """
-- Valid, answerable question → question_bank
INSERT INTO question_bank (id, name, lead_id, franchisor_id, is_active, created_at, updated_at)
VALUES (
    'uuid-here',
    'What is the ROI of this franchise?',
    'lead-uuid-here',
    'franchisor-uuid-here',
    true,
    NOW(),
    NOW()
);""",
                    "escalation_question_bank_insert": """
-- Unanswerable, unclear, or out-of-scope question → escalation_question_bank
INSERT INTO escalation_question_bank (id, name, lead_id, franchisor_id, answer, support_status, is_active, created_at, updated_at)
VALUES (
    'uuid-here',
    'What is your GST number?',
    'lead-uuid-here',
    'franchisor-uuid-here',
    '[]'::jsonb,
    'pending',
    true,
    NOW(),
    NOW()
);"""
                }
            }
            
            return success_response(
                title="Classification Examples",
                description="Examples of question classifications and SQL statements",
                data=examples
            )
            
        except Exception as e:
            logger.error(f"Error getting classification examples: {str(e)}")
            return error_response(
                title="Examples Error",
                description=f"An error occurred getting examples: {str(e)}",
                error_code=500
            )
