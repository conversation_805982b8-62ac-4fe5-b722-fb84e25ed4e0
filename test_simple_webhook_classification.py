#!/usr/bin/env python3
"""
Simple Test for Question Classification with Kudosity SMS Webhook
Tests the webhook with the exact request body provided
"""

import asyncio
import json
import httpx
from datetime import datetime

# Exact webhook payload as provided
WEBHOOK_PAYLOAD = {
    "event_type": "SMS_INBOUND",
    "timestamp": "2025-07-02T14:39:54Z",
    "webhook_id": "1b39ae29-f723-4426-9d35-efa763959c1b",
    "webhook_name": "openxcell_webbook",
    "mo": {
        "type": "SMS",
        "id": "93d34863-2756-4d27-8e11-66441081da88",
        "sender": "61430250075",
        "recipient": "61430250079",
        "message": "Who is the CEO of the company?",
        "last_message": {
            "id": "7f507779-6a4e-48bd-a7f1-488a29d82440",
            "type": "SMS",
            "sender": "61430250079",
            "recipient": "61430250079",
            "message": "hello there 3",
            "message_ref": "a0322322-5a6b-48cc-9c03-92425887c3c5"
        }
    }
}

async def test_webhook():
    """Test the Kudosity SMS webhook with question classification"""
    
    print("🚀 GROWTHHIVE QUESTION CLASSIFICATION WEBHOOK TEST")
    print("=" * 80)
    
    # Test different message variations
    test_messages = [
        "Who is the CEO of the company?",
        "What's the ROI of this franchise?",
        "How much is the initial investment?",
        "Do you sell ice cream trucks?",
        "Help",
        "I need immediate assistance"
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for i, message in enumerate(test_messages, 1):
            print(f"\n📝 TEST {i}: Testing message classification")
            print(f"Message: '{message}'")
            print("-" * 50)
            
            # Update the payload with the test message
            test_payload = WEBHOOK_PAYLOAD.copy()
            test_payload["mo"]["message"] = message
            
            try:
                # Test the Kudosity webhook endpoint
                response = await client.post(
                    "http://localhost:8000/api/webhooks/webhooks/kudosity",
                    json=test_payload,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"📊 Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ Webhook processed successfully!")
                    print(f"📋 Response: {json.dumps(result, indent=2)}")
                    
                    # Check if question classification is working
                    if "data" in result:
                        print("🎯 Question classification detected in response")
                    else:
                        print("⚠️  No classification data found in response")
                        
                elif response.status_code == 404:
                    print("❌ Endpoint not found. Checking available endpoints...")
                    # Try alternative endpoint
                    alt_response = await client.post(
                        "http://localhost:8000/api/v1/webhooks/kudosity",
                        json=test_payload,
                        headers={"Content-Type": "application/json"}
                    )
                    print(f"📊 Alternative Status Code: {alt_response.status_code}")
                    if alt_response.status_code == 200:
                        result = alt_response.json()
                        print("✅ Webhook processed successfully with alternative endpoint!")
                        print(f"📋 Response: {json.dumps(result, indent=2)}")
                    else:
                        print(f"❌ Alternative endpoint also failed: {alt_response.text}")
                        
                else:
                    print(f"❌ Webhook failed with status {response.status_code}")
                    print(f"📋 Error: {response.text}")
                    
            except Exception as e:
                print(f"❌ Error testing webhook: {str(e)}")
            
            print("\n" + "=" * 50)

async def test_health_endpoint():
    """Test the webhook health endpoint"""
    print("\n🏥 TESTING WEBHOOK HEALTH")
    print("=" * 40)
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            response = await client.get("http://localhost:8000/api/webhooks/webhooks/health")
            print(f"📊 Health Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Health check passed!")
                print(f"📋 Health Response: {json.dumps(result, indent=2)}")
            else:
                print(f"❌ Health check failed: {response.text}")
                
        except Exception as e:
            print(f"❌ Health check error: {str(e)}")

async def main():
    """Main test function"""
    print("🔍 Starting Kudosity SMS Webhook Test")
    print("=" * 80)
    
    # Test health endpoint first
    await test_health_endpoint()
    
    # Test webhook functionality
    await test_webhook()
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 