#!/usr/bin/env python3
"""
Question Classification Demo Script
Demonstrates the AI-powered question classification system
"""

import asyncio
import sys
import os
import uuid
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.agents.question_classification import QuestionClassificationAgent
from app.agents.base import AgentConfig, AgentRole
from app.services.question_classification_service import QuestionClassificationService
from app.core.database.connection import get_db


class QuestionClassificationDemo:
    """Demo class for question classification system"""
    
    def __init__(self):
        self.agent = None
        self.service = None
    
    def setup_agent(self):
        """Setup the classification agent"""
        config = AgentConfig(
            role=AgentRole.QUESTION_ANSWERING,
            model="gpt-4o",
            temperature=0.3,
            max_tokens=1000,
            description="Demo classification agent"
        )
        self.agent = QuestionClassificationAgent(config)
        print("✅ Question Classification Agent initialized")
    
    async def setup_service(self):
        """Setup the classification service"""
        async for db in get_db():
            self.service = QuestionClassificationService(db)
            print("✅ Question Classification Service initialized")
            break
    
    def print_header(self, title: str):
        """Print a formatted header"""
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)
    
    def print_result(self, question: str, result: dict):
        """Print classification result in a formatted way"""
        print(f"\n📝 Question: {question}")
        print("-" * 50)
        
        if result.get("success"):
            data = result.get("data", {})
            classification = data.get("classification", "unknown")
            table = data.get("table", "unknown")
            
            if classification == "answerable":
                print(f"✅ Classification: ANSWERABLE")
                print(f"📊 Table: {table}")
                print(f"💡 Answer: {data.get('answer', 'No answer provided')}")
            else:
                print(f"⚠️  Classification: ESCALATION")
                print(f"📊 Table: {table}")
                print(f"🔍 Reason: {data.get('reason', 'No reason provided')}")
            
            # Print SQL statement
            sql = data.get("sql_statement", "")
            if sql:
                print(f"\n🗄️  SQL Statement:")
                print(sql)
        else:
            print(f"❌ Error: {result.get('message', {}).get('description', 'Unknown error')}")
    
    async def demo_individual_questions(self):
        """Demo individual question classification"""
        self.print_header("INDIVIDUAL QUESTION CLASSIFICATION DEMO")
        
        # Test questions covering different scenarios
        test_questions = [
            {
                "question": "What's the ROI of this franchise?",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",  # Coochie Hydrogreen
                "expected": "answerable"
            },
            {
                "question": "What are the training requirements?",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",
                "expected": "answerable"
            },
            {
                "question": "What's your GST number?",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",
                "expected": "escalation"
            },
            {
                "question": "Help",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",
                "expected": "escalation"
            },
            {
                "question": "Do you sell ice cream trucks?",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",
                "expected": "escalation"
            },
            {
                "question": "How much is the initial investment?",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",
                "expected": "answerable"
            }
        ]
        
        for i, test_case in enumerate(test_questions, 1):
            print(f"\n[{i}/{len(test_questions)}] Testing question...")
            
            result = await self.service.classify_question(
                question_text=test_case["question"],
                lead_id=test_case["lead_id"],
                franchisor_id=test_case["franchisor_id"],
                context={"demo": True}
            )
            
            self.print_result(test_case["question"], result)
            
            # Check if classification matches expectation
            if result.get("success"):
                actual = result.get("data", {}).get("classification", "unknown")
                expected = test_case["expected"]
                if actual == expected:
                    print(f"✅ Classification matches expectation: {expected}")
                else:
                    print(f"⚠️  Classification mismatch: expected {expected}, got {actual}")
    
    async def demo_batch_classification(self):
        """Demo batch question classification"""
        self.print_header("BATCH QUESTION CLASSIFICATION DEMO")
        
        batch_questions = [
            {
                "question_text": "What support do you provide to franchisees?",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe"
            },
            {
                "question_text": "What are the ongoing fees?",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe"
            },
            {
                "question_text": "I need immediate assistance with my application",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe"
            },
            {
                "question_text": "What's the territory size?",
                "lead_id": str(uuid.uuid4()),
                "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe"
            }
        ]
        
        print(f"📦 Processing batch of {len(batch_questions)} questions...")
        
        result = await self.service.batch_classify_questions(batch_questions)
        
        if result.get("success"):
            data = result.get("data", {})
            print(f"\n📊 Batch Results:")
            print(f"   Total Questions: {data.get('total_questions', 0)}")
            print(f"   Successful: {data.get('successful_classifications', 0)}")
            print(f"   Failed: {data.get('failed_classifications', 0)}")
            
            # Show individual results
            for result_item in data.get("results", []):
                question = result_item.get("question", "")
                individual_result = result_item.get("result", {})
                print(f"\n[{result_item.get('index', 0) + 1}] {question[:50]}...")
                if individual_result.get("success"):
                    classification = individual_result.get("data", {}).get("classification", "unknown")
                    table = individual_result.get("data", {}).get("table", "unknown")
                    print(f"    ✅ {classification.upper()} → {table}")
                else:
                    print(f"    ❌ Error: {individual_result.get('message', {}).get('description', 'Unknown')}")
        else:
            print(f"❌ Batch processing failed: {result.get('message', {}).get('description', 'Unknown error')}")
    
    async def demo_classification_examples(self):
        """Demo classification examples"""
        self.print_header("CLASSIFICATION EXAMPLES AND DOCUMENTATION")
        
        result = await self.service.get_classification_examples()
        
        if result.get("success"):
            data = result.get("data", {})
            
            print("\n📚 ANSWERABLE QUESTIONS (→ question_bank):")
            for example in data.get("answerable_questions", []):
                print(f"   • {example.get('question', '')}")
                print(f"     Reason: {example.get('reason', '')}")
            
            print("\n⚠️  ESCALATION QUESTIONS (→ escalation_question_bank):")
            for example in data.get("escalation_questions", []):
                print(f"   • {example.get('question', '')}")
                print(f"     Reason: {example.get('reason', '')}")
            
            print("\n🗄️  SQL EXAMPLES:")
            sql_examples = data.get("sql_examples", {})
            for table, sql in sql_examples.items():
                print(f"\n{table}:")
                print(sql)
        else:
            print(f"❌ Failed to get examples: {result.get('message', {}).get('description', 'Unknown error')}")
    
    async def run_demo(self):
        """Run the complete demo"""
        print("🚀 Question Classification System Demo")
        print("=====================================")
        print("This demo shows how the AI agent classifies questions into:")
        print("1. Valid, answerable questions → question_bank")
        print("2. Unanswerable, unclear, or out-of-scope questions → escalation_question_bank")
        
        try:
            # Setup
            self.setup_agent()
            await self.setup_service()
            
            # Run demos
            await self.demo_individual_questions()
            await self.demo_batch_classification()
            await self.demo_classification_examples()
            
            self.print_header("DEMO COMPLETE")
            print("✅ All demonstrations completed successfully!")
            print("\n📝 Key Features Demonstrated:")
            print("   • Individual question classification")
            print("   • Batch question processing")
            print("   • SQL statement generation")
            print("   • RAG system integration")
            print("   • Error handling and validation")
            print("\n🔗 API Endpoints Available:")
            print("   • POST /api/v1/question-classification/classify")
            print("   • POST /api/v1/question-classification/classify/batch")
            print("   • GET  /api/v1/question-classification/examples")
            print("   • POST /api/v1/question-classification/test")
            
        except Exception as e:
            print(f"\n❌ Demo failed with error: {str(e)}")
            import traceback
            traceback.print_exc()


async def main():
    """Main function to run the demo"""
    demo = QuestionClassificationDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
