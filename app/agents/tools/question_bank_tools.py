"""
Question Bank Tools for Agent System
Tools for inserting questions into question_bank and escalation_question_bank tables
"""

from typing import Optional, List, Dict, Any
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import structlog
import uuid
from datetime import datetime
from sqlalchemy import select, text

from app.core.database.connection import get_db
from app.models.lead_reference import QuestionBank, EscalationQuestionBank

logger = structlog.get_logger()


class InsertQuestionBankInput(BaseModel):
    """Input schema for inserting into question_bank"""
    question_text: str = Field(description="The question text to store")
    lead_id: Optional[str] = Field(None, description="ID of the associated lead")
    franchisor_id: Optional[str] = Field(None, description="ID of the associated franchisor")


class InsertQuestionBankTool(BaseTool):
    """Tool for inserting valid, answerable questions into question_bank"""
    
    name: str = "insert_question_bank"
    description: str = "Insert a valid, answerable question into the question_bank table"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.args_schema = InsertQuestionBankInput
    
    async def _arun(self, **kwargs) -> str:
        """Insert question into question_bank"""
        try:
            question_text = kwargs["question_text"]
            lead_id = kwargs.get("lead_id")
            franchisor_id = kwargs.get("franchisor_id")
            
            # Generate UUID for the new record
            question_id = str(uuid.uuid4())
            
            # Create SQL INSERT statement
            sql_statement = f"""
-- Valid, answerable question → question_bank
INSERT INTO question_bank (id, name, lead_id, franchisor_id, is_active, created_at, updated_at)
VALUES (
    '{question_id}',
    '{question_text.replace("'", "''")}',
    {f"'{lead_id}'" if lead_id else 'NULL'},
    {f"'{franchisor_id}'" if franchisor_id else 'NULL'},
    true,
    NOW(),
    NOW()
);
"""
            
            # Execute the insertion
            async for db in get_db():
                # Create the question bank entry
                question_entry = QuestionBank(
                    id=uuid.UUID(question_id),
                    name=question_text,
                    lead_id=uuid.UUID(lead_id) if lead_id else None,
                    franchisor_id=uuid.UUID(franchisor_id) if franchisor_id else None,
                    is_active=True
                )
                
                db.add(question_entry)
                await db.commit()
                
                logger.info(f"Inserted question into question_bank: {question_id}")
                return f"Question inserted into question_bank successfully.\n\nSQL Statement:\n{sql_statement}"
                
        except Exception as e:
            logger.error(f"Error inserting into question_bank: {str(e)}")
            return f"Error inserting into question_bank: {str(e)}"


class InsertEscalationQuestionBankInput(BaseModel):
    """Input schema for inserting into escalation_question_bank"""
    question_text: str = Field(description="The question text to store")
    lead_id: Optional[str] = Field(None, description="ID of the associated lead")
    franchisor_id: Optional[str] = Field(None, description="ID of the associated franchisor")
    reason: Optional[str] = Field(None, description="Reason for escalation")


class InsertEscalationQuestionBankTool(BaseTool):
    """Tool for inserting unanswerable/unclear questions into escalation_question_bank"""
    
    name: str = "insert_escalation_question_bank"
    description: str = "Insert an unanswerable, unclear, or out-of-scope question into the escalation_question_bank table"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.args_schema = InsertEscalationQuestionBankInput
    
    async def _arun(self, **kwargs) -> str:
        """Insert question into escalation_question_bank"""
        try:
            question_text = kwargs["question_text"]
            lead_id = kwargs.get("lead_id")
            franchisor_id = kwargs.get("franchisor_id")
            reason = kwargs.get("reason", "Requires human support")
            
            # Generate UUID for the new record
            question_id = str(uuid.uuid4())
            
            # Create SQL INSERT statement
            sql_statement = f"""
-- Unanswerable, unclear, or out-of-scope question → escalation_question_bank
INSERT INTO escalation_question_bank (id, name, lead_id, franchisor_id, answer, support_status, is_active, created_at, updated_at)
VALUES (
    '{question_id}',
    '{question_text.replace("'", "''")}',
    {f"'{lead_id}'" if lead_id else 'NULL'},
    {f"'{franchisor_id}'" if franchisor_id else 'NULL'},
    '[]'::jsonb,
    'pending',
    true,
    NOW(),
    NOW()
);
"""
            
            # Execute the insertion
            async for db in get_db():
                # Create the escalation question bank entry
                escalation_entry = EscalationQuestionBank(
                    id=uuid.UUID(question_id),
                    name=question_text,
                    lead_id=uuid.UUID(lead_id) if lead_id else None,
                    franchisor_id=uuid.UUID(franchisor_id) if franchisor_id else None,
                    answer=[],  # Empty array as specified
                    support_status="pending",
                    is_active=True
                )
                
                db.add(escalation_entry)
                await db.commit()
                
                logger.info(f"Inserted question into escalation_question_bank: {question_id}")
                return f"Question inserted into escalation_question_bank successfully.\nReason: {reason}\n\nSQL Statement:\n{sql_statement}"
                
        except Exception as e:
            logger.error(f"Error inserting into escalation_question_bank: {str(e)}")
            return f"Error inserting into escalation_question_bank: {str(e)}"


class AnalyzeQuestionInput(BaseModel):
    """Input schema for analyzing question answerability"""
    question_text: str = Field(description="The question to analyze")
    context: Optional[str] = Field(None, description="Available context or knowledge base content")
    rag_answer: Optional[str] = Field(None, description="Answer from RAG system")


class AnalyzeQuestionTool(BaseTool):
    """Tool for analyzing whether a question is answerable or needs escalation"""
    
    name: str = "analyze_question"
    description: str = "Analyze a question to determine if it's answerable or needs escalation"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.args_schema = AnalyzeQuestionInput
    
    async def _arun(self, **kwargs) -> str:
        """Analyze question answerability"""
        try:
            question_text = kwargs["question_text"]
            context = kwargs.get("context", "")
            rag_answer = kwargs.get("rag_answer", "")
            
            # Define criteria for escalation
            escalation_keywords = [
                "gst number", "tax id", "specific contact", "personal information",
                "legal advice", "financial advice", "medical advice",
                "complaint", "refund", "cancel", "dispute",
                "urgent", "emergency", "immediate assistance"
            ]
            
            unclear_indicators = [
                "what", "how", "when", "where", "why"  # Very vague questions
            ]
            
            # Check if question is too vague or unclear
            question_lower = question_text.lower()
            word_count = len(question_text.split())
            
            # Analysis logic
            needs_escalation = False
            reason = ""
            
            # Check for escalation keywords
            for keyword in escalation_keywords:
                if keyword in question_lower:
                    needs_escalation = True
                    reason = f"Contains escalation keyword: '{keyword}'"
                    break
            
            # Check if question is too short/vague
            if not needs_escalation and word_count < 3:
                needs_escalation = True
                reason = "Question too short/vague"
            
            # Check RAG answer quality if provided
            if not needs_escalation and rag_answer:
                if any(phrase in rag_answer.lower() for phrase in [
                    "i don't know", "i cannot answer", "no information available",
                    "not found", "unclear", "insufficient information"
                ]):
                    needs_escalation = True
                    reason = "RAG system could not provide adequate answer"
            
            # Check if context is insufficient
            if not needs_escalation and not context and not rag_answer:
                needs_escalation = True
                reason = "No context or knowledge base available"
            
            result = {
                "needs_escalation": needs_escalation,
                "reason": reason,
                "question_length": word_count,
                "analysis": "Question analyzed for answerability"
            }
            
            return str(result)
                
        except Exception as e:
            logger.error(f"Error analyzing question: {str(e)}")
            return f"Error analyzing question: {str(e)}"
