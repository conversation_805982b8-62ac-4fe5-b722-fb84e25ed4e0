"""
Question Classification Agent
Classifies incoming questions into answerable vs escalation categories
"""

from typing import Dict, Any, List, Optional
import structlog
import json
from langchain_core.messages import AIMessage

from .base import BaseAgent, AgentState
from .tools.registry import tool_registry
from docqa.central_api import ask_question

logger = structlog.get_logger()


class QuestionClassificationAgent(BaseAgent):
    """
    Agent responsible for classifying questions into:
    1. Valid, answerable questions → question_bank
    2. Unanswerable, unclear, or out-of-scope questions → escalation_question_bank
    """
    
    def _initialize_tools(self):
        """Initialize classification-specific tools"""
        # Import and register question bank tools
        from .tools.question_bank_tools import (
            InsertQuestionBankTool,
            InsertEscalationQuestionBankTool,
            AnalyzeQuestionTool
        )
        
        # Register the tools in the registry
        tool_registry.register_tool("insert_question_bank", InsertQuestionBankTool)
        tool_registry.register_tool("insert_escalation_question_bank", InsertEscalationQuestionBankTool)
        tool_registry.register_tool("analyze_question", AnalyzeQuestionTool)
        
        # Get tools for this agent
        tool_names = [
            "insert_question_bank", 
            "insert_escalation_question_bank", 
            "analyze_question",
            "search_documents"
        ]
        self.tools = tool_registry.get_tools_by_names(tool_names)
    
    async def process_state(self, state: AgentState) -> AgentState:
        """Process question classification requests"""
        try:
            question_text = state.get("user_input", "")
            context = state.get("context", {})
            lead_id = state.get("lead_id")
            franchisor_id = state.get("franchisor_id")
            
            if not question_text:
                state["error"] = "No question text provided"
                return state
            
            logger.info(f"Classifying question: {question_text}")
            
            # Step 1: Attempt to answer using RAG system
            rag_answer = await self._attempt_rag_answer(question_text, franchisor_id)
            state["rag_answer"] = rag_answer
            
            # Step 2: Analyze question answerability
            analysis_result = await self._analyze_question(question_text, context, rag_answer)
            state["analysis"] = analysis_result
            
            # Step 3: Classify and insert into appropriate table
            classification_result = await self._classify_and_insert(
                question_text, lead_id, franchisor_id, analysis_result, rag_answer
            )
            
            state["classification_result"] = classification_result
            state["response"] = classification_result
            
            return state
            
        except Exception as e:
            logger.error(f"Error in question classification: {str(e)}")
            state["error"] = str(e)
            return state
    
    async def _attempt_rag_answer(self, question: str, franchisor_id: Optional[str] = None) -> str:
        """Attempt to answer the question using the RAG system"""
        try:
            # Prepare request for DocQA system
            request = {
                "question": question,
                "top_k": 5,
                "similarity_threshold": 0.1,  # Low threshold for better coverage
                "temperature": 0.3,
                "max_tokens": 500
            }
            
            if franchisor_id:
                request["franchisor_id"] = franchisor_id
            
            # Use the central DocQA API
            result = await ask_question(request)
            
            if isinstance(result, dict) and "answer" in result:
                return result["answer"]
            else:
                return "No answer available from knowledge base"
                
        except Exception as e:
            logger.error(f"Error getting RAG answer: {str(e)}")
            return f"Error accessing knowledge base: {str(e)}"
    
    async def _analyze_question(self, question: str, context: Dict[str, Any], rag_answer: str) -> Dict[str, Any]:
        """Analyze question to determine if it needs escalation"""
        try:
            # Use the analyze_question tool
            analysis_tool = next((tool for tool in self.tools if tool.name == "analyze_question"), None)
            if not analysis_tool:
                return {"needs_escalation": True, "reason": "Analysis tool not available"}
            
            result = await analysis_tool._arun(
                question_text=question,
                context=str(context),
                rag_answer=rag_answer
            )
            
            # Parse the result
            try:
                return eval(result)  # Safe since we control the tool output
            except:
                return {"needs_escalation": True, "reason": "Analysis parsing error"}
                
        except Exception as e:
            logger.error(f"Error analyzing question: {str(e)}")
            return {"needs_escalation": True, "reason": f"Analysis error: {str(e)}"}
    
    async def _classify_and_insert(
        self, 
        question: str, 
        lead_id: Optional[str], 
        franchisor_id: Optional[str],
        analysis: Dict[str, Any],
        rag_answer: str
    ) -> Dict[str, Any]:
        """Classify question and insert into appropriate table"""
        try:
            needs_escalation = analysis.get("needs_escalation", True)
            reason = analysis.get("reason", "Unknown")
            
            if needs_escalation:
                # Insert into escalation_question_bank
                escalation_tool = next((tool for tool in self.tools if tool.name == "insert_escalation_question_bank"), None)
                if escalation_tool:
                    result = await escalation_tool._arun(
                        question_text=question,
                        lead_id=lead_id,
                        franchisor_id=franchisor_id,
                        reason=reason
                    )
                    
                    return {
                        "classification": "escalation",
                        "table": "escalation_question_bank",
                        "reason": reason,
                        "sql_result": result,
                        "rag_answer": rag_answer,
                        "analysis": analysis
                    }
            else:
                # Insert into question_bank
                question_tool = next((tool for tool in self.tools if tool.name == "insert_question_bank"), None)
                if question_tool:
                    result = await question_tool._arun(
                        question_text=question,
                        lead_id=lead_id,
                        franchisor_id=franchisor_id
                    )
                    
                    return {
                        "classification": "answerable",
                        "table": "question_bank",
                        "answer": rag_answer,
                        "sql_result": result,
                        "analysis": analysis
                    }
            
            return {
                "classification": "error",
                "error": "Could not find appropriate tool for insertion"
            }
            
        except Exception as e:
            logger.error(f"Error in classification and insertion: {str(e)}")
            return {
                "classification": "error",
                "error": str(e)
            }
    
    async def classify_question(
        self, 
        question_text: str, 
        lead_id: Optional[str] = None, 
        franchisor_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Main method to classify a question
        
        Args:
            question_text: The question to classify
            lead_id: Optional lead ID
            franchisor_id: Optional franchisor ID
            context: Optional context information
            
        Returns:
            Classification result with SQL statements
        """
        try:
            # Prepare state
            state: AgentState = {
                "user_input": question_text,
                "lead_id": lead_id,
                "franchisor_id": franchisor_id,
                "context": context or {},
                "messages": [],
                "session_id": f"classification_{lead_id or 'unknown'}",
                "intent": "classify_question",
                "next_action": None,
                "lead_data": None,
                "lead_status": None,
                "document_id": None,
                "document_content": None,
                "search_results": None,
                "meeting_data": None,
                "availability": None,
                "conversation_history": [],
                "response": None,
                "error": None,
                "metadata": {"timestamp": None}
            }
            
            # Process the classification
            result_state = await self.process_state(state)
            
            if result_state.get("error"):
                return {
                    "success": False,
                    "error": result_state["error"]
                }
            
            return {
                "success": True,
                "result": result_state.get("classification_result", {}),
                "rag_answer": result_state.get("rag_answer"),
                "analysis": result_state.get("analysis")
            }
            
        except Exception as e:
            logger.error(f"Error in classify_question: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
