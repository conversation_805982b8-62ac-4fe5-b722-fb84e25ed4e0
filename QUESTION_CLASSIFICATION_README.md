# Question Classification System

## Overview

The Question Classification System is an AI-powered solution that automatically classifies incoming questions into two categories:

1. **Valid, answerable questions** → inserted into `question_bank`
2. **Unanswerable, unclear, or out-of-scope questions** → inserted into `escalation_question_bank`

## Features

- **AI-Powered Classification**: Uses GPT-4 and RAG system for intelligent question analysis
- **Automatic SQL Generation**: Generates PostgreSQL INSERT statements for both tables
- **Context-Aware**: Integrates with existing document knowledge base
- **Batch Processing**: Supports classification of multiple questions at once
- **Comprehensive API**: RESTful endpoints for all classification operations
- **Detailed Analysis**: Provides reasoning for each classification decision

## Architecture

### Components

1. **QuestionClassificationAgent**: Core AI agent that performs classification
2. **Database Tools**: Tools for inserting into question_bank and escalation_question_bank
3. **Classification Service**: Business logic layer for orchestrating classification
4. **API Endpoints**: RESTful interface for external integration
5. **RAG Integration**: Uses existing DocQA system for answering attempts

### Classification Logic

The system follows this process:

1. **Analyze Question**: Check for clarity, length, and intent
2. **Attempt RAG Answer**: Try to answer using available knowledge base
3. **Evaluate Answer Quality**: Determine if the answer is satisfactory
4. **Classify and Insert**: Route to appropriate table with SQL generation

## Database Schema

### question_bank Table
```sql
CREATE TABLE question_bank (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    lead_id UUID,
    franchisor_id UUID,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### escalation_question_bank Table
```sql
CREATE TABLE escalation_question_bank (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR NOT NULL,
    lead_id UUID,
    franchisor_id UUID,
    answer JSONB DEFAULT '[]',
    support_status VARCHAR DEFAULT 'pending',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### 1. Classify Single Question
```http
POST /api/v1/question-classification/classify
```

**Request:**
```json
{
    "question_text": "What's the ROI of this franchise?",
    "lead_id": "550e8400-e29b-41d4-a716-************",
    "franchisor_id": "569976f2-d845-4615-8a91-96e18086adbe",
    "context": {
        "brochure_text": "Additional context..."
    }
}
```

**Response:**
```json
{
    "success": true,
    "status": "success",
    "message": {
        "title": "Question Classified as Answerable",
        "description": "Question inserted into question_bank table"
    },
    "data": {
        "classification": "answerable",
        "table": "question_bank",
        "question": "What's the ROI of this franchise?",
        "answer": "The ROI is typically 15-20% annually based on our financial projections.",
        "sql_statement": "INSERT INTO question_bank (id, name, lead_id, franchisor_id, is_active, created_at, updated_at) VALUES (...);",
        "analysis": {
            "needs_escalation": false,
            "reason": "Question can be answered from available information",
            "question_length": 7
        }
    }
}
```

### 2. Batch Classification
```http
POST /api/v1/question-classification/classify/batch
```

### 3. Get Examples
```http
GET /api/v1/question-classification/examples
```

### 4. Test Classification
```http
POST /api/v1/question-classification/test?question=What's the ROI?
```

## Classification Examples

### Answerable Questions → question_bank

| Question | Reason |
|----------|--------|
| "What's the ROI of this franchise?" | Can be answered from brochure financial information |
| "What are the training requirements?" | Training information available in franchise documents |
| "How much is the initial investment?" | Investment details typically in franchise brochures |
| "What support do you provide?" | Support information available in documentation |

### Escalation Questions → escalation_question_bank

| Question | Reason |
|----------|--------|
| "Do you sell ice cream trucks?" | Not mentioned in available franchise documents |
| "What's your GST number?" | Requires specific business information not in brochures |
| "Help" | Too vague and unclear |
| "I need immediate assistance" | Contains escalation keyword |

## Usage Examples

### Python Service Usage
```python
from app.services.question_classification_service import QuestionClassificationService

# Initialize service
service = QuestionClassificationService(db_session)

# Classify a question
result = await service.classify_question(
    question_text="What's the ROI of this franchise?",
    lead_id="lead-uuid",
    franchisor_id="franchisor-uuid"
)

print(result["data"]["classification"])  # "answerable" or "escalation"
print(result["data"]["sql_statement"])   # Generated SQL
```

### Agent Usage
```python
from app.agents.question_classification import QuestionClassificationAgent
from app.agents.base import AgentConfig, AgentRole

# Initialize agent
config = AgentConfig(
    role=AgentRole.QUESTION_ANSWERING,
    model="gpt-4o",
    temperature=0.3
)
agent = QuestionClassificationAgent(config)

# Classify question
result = await agent.classify_question(
    question_text="What's the ROI?",
    lead_id="lead-uuid",
    franchisor_id="franchisor-uuid"
)
```

## Testing

### Run Tests
```bash
# Run all question classification tests
pytest tests/test_question_classification.py -v

# Run specific test
pytest tests/test_question_classification.py::TestQuestionClassificationAgent::test_classify_answerable_question -v
```

### Demo Script
```bash
# Run the demonstration script
python scripts/demo_question_classification.py
```

## Configuration

### Environment Variables
```bash
# OpenAI API Key (required for AI classification)
OPENAI_API_KEY=your_openai_api_key

# Database connection (required for SQL operations)
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/growthhive

# Optional: Adjust classification parameters
CLASSIFICATION_TEMPERATURE=0.3
CLASSIFICATION_MAX_TOKENS=1000
```

### Agent Configuration
```python
config = AgentConfig(
    role=AgentRole.QUESTION_ANSWERING,
    model="gpt-4o",  # or "gpt-3.5-turbo"
    temperature=0.3,  # Lower = more consistent
    max_tokens=1000,  # Adjust based on needs
    description="Question classification agent"
)
```

## Integration

### Webhook Integration
The classification system can be integrated with webhook endpoints:

```python
# In webhook handler
from app.services.question_classification_service import QuestionClassificationService

async def handle_sms_webhook(message: str, lead_id: str):
    service = QuestionClassificationService(db)
    result = await service.classify_question(
        question_text=message,
        lead_id=lead_id,
        franchisor_id=get_franchisor_id()
    )
    
    if result["data"]["classification"] == "answerable":
        # Send AI answer back to user
        return result["data"]["answer"]
    else:
        # Route to human support
        return "Thank you for your question. A team member will get back to you soon."
```

### CRM Integration
Classification results can be synced to CRM systems:

```python
# Sync to Zoho CRM
if result["data"]["classification"] == "escalation":
    await zoho_service.create_support_ticket({
        "question": result["data"]["question"],
        "lead_id": result["data"]["lead_id"],
        "priority": "high" if "urgent" in question.lower() else "normal"
    })
```

## Monitoring and Analytics

### Classification Metrics
- Total questions classified
- Answerable vs escalation ratio
- Response accuracy
- Processing time

### Database Queries
```sql
-- Count questions by classification
SELECT 
    'answerable' as type, COUNT(*) as count 
FROM question_bank 
WHERE created_at >= NOW() - INTERVAL '24 hours'
UNION ALL
SELECT 
    'escalation' as type, COUNT(*) as count 
FROM escalation_question_bank 
WHERE created_at >= NOW() - INTERVAL '24 hours';

-- Most common escalation reasons
SELECT 
    support_status, 
    COUNT(*) as count 
FROM escalation_question_bank 
GROUP BY support_status 
ORDER BY count DESC;
```

## Troubleshooting

### Common Issues

1. **Classification Always Returns Escalation**
   - Check OpenAI API key configuration
   - Verify RAG system is working
   - Check document ingestion status

2. **SQL Insertion Fails**
   - Verify database connection
   - Check table schemas match models
   - Validate UUID formats

3. **Poor Classification Accuracy**
   - Adjust temperature settings
   - Update escalation keywords
   - Improve RAG system content

### Debug Mode
```python
# Enable debug logging
import logging
logging.getLogger("app.agents.question_classification").setLevel(logging.DEBUG)
```

## Future Enhancements

- **Machine Learning**: Train custom classification models
- **Multi-language**: Support for non-English questions
- **Confidence Scoring**: Add confidence levels to classifications
- **Auto-learning**: Improve classification based on feedback
- **Custom Rules**: Allow custom classification rules per franchisor
