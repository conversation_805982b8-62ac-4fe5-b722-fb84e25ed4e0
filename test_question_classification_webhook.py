#!/usr/bin/env python3
"""
Test Question Classification with Kudosity SMS Webhook
Tests the question classification functionality using the Kudosity webhook endpoint
"""

import asyncio
import json
import httpx
from datetime import datetime
from typing import Dict, Any

# Test webhook payload with different phone numbers to avoid session conflicts
def create_test_payload(message: str, phone_number: str = None) -> Dict[str, Any]:
    """Create a test webhook payload with the given message and phone number"""
    if phone_number is None:
        phone_number = f"61430{hash(message) % 100000:05d}"  # Generate unique phone number
    
    return {
        "event_type": "SMS_INBOUND",
        "timestamp": "2025-07-02T14:39:54Z",
        "webhook_id": "1b39ae29-f723-4426-9d35-efa763959c1b",
        "webhook_name": "openxcell_webbook",
        "mo": {
            "type": "SMS",
            "id": f"test-{hash(message)}",
            "sender": phone_number,
            "recipient": "61430250079",
            "message": message,
            "last_message": {
                "id": f"last-{hash(message)}",
                "type": "SMS",
                "sender": "61430250079",
                "recipient": "61430250079",
                "message": "Previous message",
                "message_ref": f"ref-{hash(message)}"
            }
        }
    }

async def test_question_classification():
    """Test the question classification functionality"""
    
    print("🚀 GROWTHHIVE QUESTION CLASSIFICATION TEST")
    print("=" * 80)
    
    # Test questions with expected classifications
    test_cases = [
        {
            "question": "Who is the CEO of the company?",
            "expected": "answerable",
            "phone": "61430123456"
        },
        {
            "question": "What's the ROI of this franchise?",
            "expected": "answerable", 
            "phone": "61430234567"
        },
        {
            "question": "How much is the initial investment?",
            "expected": "answerable",
            "phone": "61430345678"
        },
        {
            "question": "What are the training requirements?",
            "expected": "answerable",
            "phone": "61430456789"
        },
        {
            "question": "Do you sell ice cream trucks?",
            "expected": "escalation",
            "phone": "61430567890"
        },
        {
            "question": "What's your GST number?",
            "expected": "escalation",
            "phone": "61430678901"
        },
        {
            "question": "Help",
            "expected": "escalation",
            "phone": "61430789012"
        },
        {
            "question": "I need immediate assistance",
            "expected": "escalation",
            "phone": "61430890123"
        }
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 TEST {i}: Question Classification")
            print(f"Question: '{test_case['question']}'")
            print(f"Expected: {test_case['expected']}")
            print(f"Phone: {test_case['phone']}")
            print("-" * 60)
            
            # Create payload with unique phone number
            payload = create_test_payload(test_case['question'], test_case['phone'])
            
            try:
                # Test the Kudosity webhook endpoint
                response = await client.post(
                    "http://localhost:8000/api/webhooks/webhooks/kudosity",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"📊 Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ Webhook processed successfully!")
                    
                    # Extract key information
                    ai_response = result.get('data', {}).get('ai_response', 'No response')
                    ai_success = result.get('data', {}).get('ai_success', False)
                    ai_error = result.get('data', {}).get('ai_error', None)
                    
                    print(f"🤖 AI Response: {ai_response}")
                    print(f"✅ AI Success: {ai_success}")
                    
                    if ai_error:
                        print(f"❌ AI Error: {ai_error}")
                    
                    # Analyze the response for classification indicators
                    classification_analysis = analyze_classification_response(
                        test_case['question'], 
                        ai_response, 
                        test_case['expected']
                    )
                    print(f"🎯 Classification Analysis: {classification_analysis}")
                    
                else:
                    print(f"❌ Webhook failed with status {response.status_code}")
                    print(f"📋 Error: {response.text}")
                    
            except Exception as e:
                print(f"❌ Error testing webhook: {str(e)}")
            
            print("\n" + "=" * 60)
            
            # Small delay between requests
            await asyncio.sleep(1)

def analyze_classification_response(question: str, ai_response: str, expected: str) -> str:
    """Analyze the AI response to determine if classification is working"""
    
    # Check for escalation indicators
    escalation_keywords = [
        "out of reach", "escalation", "human", "support", "assistance",
        "apologize", "error", "try again", "contact", "team member"
    ]
    
    # Check for answerable indicators
    answerable_keywords = [
        "ceo", "roi", "investment", "training", "support", "franchise",
        "cost", "requirements", "process", "information"
    ]
    
    response_lower = ai_response.lower()
    question_lower = question.lower()
    
    # Check for escalation
    if any(keyword in response_lower for keyword in escalation_keywords):
        return "Detected as escalation"
    
    # Check for answerable content
    if any(keyword in question_lower for keyword in answerable_keywords):
        if "out of reach" not in response_lower and "error" not in response_lower:
            return "Detected as answerable"
    
    return "Classification unclear"

async def test_health_endpoint():
    """Test the webhook health endpoint"""
    print("\n🏥 TESTING WEBHOOK HEALTH")
    print("=" * 40)
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            response = await client.get("http://localhost:8000/api/webhooks/webhooks/health")
            print(f"📊 Health Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Health check passed!")
                print(f"📋 Health Response: {json.dumps(result, indent=2)}")
            else:
                print(f"❌ Health check failed: {response.text}")
                
        except Exception as e:
            print(f"❌ Health check error: {str(e)}")

async def test_question_classification_endpoint():
    """Test the dedicated question classification endpoint if available"""
    print("\n🎯 TESTING QUESTION CLASSIFICATION ENDPOINT")
    print("=" * 50)
    
    test_questions = [
        "What's the ROI of this franchise?",
        "Do you sell ice cream trucks?",
        "How much is the initial investment?"
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for question in test_questions:
            print(f"\n📝 Testing: '{question}'")
            
            try:
                # Try the question classification endpoint
                response = await client.post(
                    "http://localhost:8000/api/v1/question-classification/classify",
                    json={
                        "question_text": question,
                        "lead_id": None,
                        "franchisor_id": None
                    },
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"📊 Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ Question classification successful!")
                    print(f"📋 Response: {json.dumps(result, indent=2)}")
                else:
                    print(f"❌ Question classification failed: {response.text}")
                    
            except Exception as e:
                print(f"❌ Error: {str(e)}")

async def main():
    """Main test function"""
    print("🔍 Starting Comprehensive Question Classification Test")
    print("=" * 80)
    
    # Test health endpoint first
    await test_health_endpoint()
    
    # Test webhook functionality with question classification
    await test_question_classification()
    
    # Test dedicated question classification endpoint
    await test_question_classification_endpoint()
    
    print("\n🎉 Comprehensive test completed!")

if __name__ == "__main__":
    asyncio.run(main()) 