"""
Question Classification API Endpoints
AI-powered question classification into answerable vs escalation categories
"""

from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database.connection import get_db
from app.core.security.auth import get_current_user
from app.services.question_classification_service import QuestionClassificationService
from app.schemas.question_classification import (
    QuestionClassificationRequest,
    QuestionClassificationResponse,
    BatchQuestionClassificationRequest,
    BatchQuestionClassificationResponse,
    ClassificationExamplesResponse
)
from app.models.user import User

router = APIRouter()


def get_question_classification_service(db: AsyncSession = Depends(get_db)) -> QuestionClassificationService:
    """Dependency to get question classification service"""
    return QuestionClassificationService(db)


@router.post("/classify", response_model=QuestionClassificationResponse)
async def classify_question(
    request: QuestionClassificationRequest,
    service: QuestionClassificationService = Depends(get_question_classification_service),
    current_user: User = Depends(get_current_user)
) -> QuestionClassificationResponse:
    """
    Classify a question into answerable vs escalation categories
    
    This endpoint analyzes incoming questions and classifies them into one of two categories:
    1. **Valid, answerable questions** → inserted into `question_bank`
    2. **Unanswerable, unclear, or out-of-scope questions** → inserted into `escalation_question_bank`
    
    The system:
    - Attempts to answer using available context or knowledge base (RAG system)
    - Analyzes the question's answerability and clarity
    - Generates appropriate SQL INSERT statements
    - Returns classification results with reasoning
    
    **Examples:**
    - "What's the ROI of this franchise?" → `question_bank` (if brochure mentions ROI)
    - "Do you sell ice cream trucks?" → `escalation_question_bank` (if not mentioned anywhere)
    - "What's your GST number?" → `escalation_question_bank` (requires human support)
    """
    try:
        result = await service.classify_question(
            question_text=request.question_text,
            lead_id=request.lead_id,
            franchisor_id=request.franchisor_id,
            context=request.context
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=result.get("error_code", 500),
                detail=result.get("message", {}).get("description", "Classification failed")
            )
        
        return QuestionClassificationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during question classification: {str(e)}"
        )


@router.post("/classify/batch", response_model=BatchQuestionClassificationResponse)
async def classify_questions_batch(
    request: BatchQuestionClassificationRequest,
    service: QuestionClassificationService = Depends(get_question_classification_service),
    current_user: User = Depends(get_current_user)
) -> BatchQuestionClassificationResponse:
    """
    Classify multiple questions in batch
    
    This endpoint allows you to classify multiple questions at once, which is useful for:
    - Processing historical question data
    - Bulk classification of lead inquiries
    - Testing classification accuracy across multiple examples
    
    Each question in the batch is processed independently, and the response includes:
    - Individual classification results for each question
    - Summary statistics (successful vs failed classifications)
    - SQL statements for each classification
    """
    try:
        # Convert request to list of dictionaries
        questions = [
            {
                "question_text": q.question_text,
                "lead_id": q.lead_id,
                "franchisor_id": q.franchisor_id,
                "context": q.context
            }
            for q in request.questions
        ]
        
        result = await service.batch_classify_questions(questions)
        
        if not result.get("success"):
            raise HTTPException(
                status_code=result.get("error_code", 500),
                detail=result.get("message", {}).get("description", "Batch classification failed")
            )
        
        return BatchQuestionClassificationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during batch question classification: {str(e)}"
        )


@router.get("/examples", response_model=ClassificationExamplesResponse)
async def get_classification_examples(
    service: QuestionClassificationService = Depends(get_question_classification_service),
    current_user: User = Depends(get_current_user)
) -> ClassificationExamplesResponse:
    """
    Get examples of question classifications
    
    This endpoint provides documentation and examples of how questions are classified:
    - Examples of answerable questions that go to `question_bank`
    - Examples of escalation questions that go to `escalation_question_bank`
    - Sample SQL INSERT statements for both tables
    - Classification reasoning for each example
    
    Useful for:
    - Understanding the classification logic
    - Training team members on question types
    - Testing and validation purposes
    """
    try:
        result = await service.get_classification_examples()
        
        if not result.get("success"):
            raise HTTPException(
                status_code=result.get("error_code", 500),
                detail=result.get("message", {}).get("description", "Failed to get examples")
            )
        
        return ClassificationExamplesResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred getting classification examples: {str(e)}"
        )


@router.post("/test", response_model=QuestionClassificationResponse)
async def test_question_classification(
    question: str = Query(..., description="Question to test classification", example="What's the ROI of this franchise?"),
    lead_id: Optional[str] = Query(None, description="Optional lead ID for testing"),
    franchisor_id: Optional[str] = Query(None, description="Optional franchisor ID for testing"),
    service: QuestionClassificationService = Depends(get_question_classification_service),
    current_user: User = Depends(get_current_user)
) -> QuestionClassificationResponse:
    """
    Test question classification with a simple query parameter
    
    This is a simplified endpoint for quick testing of the classification system.
    It accepts the question as a query parameter instead of a JSON body, making it
    easier to test from browser or simple HTTP clients.
    
    **Usage:**
    ```
    POST /api/v1/question-classification/test?question=What's the ROI of this franchise?
    ```
    
    The response includes:
    - Classification result (answerable or escalation)
    - Generated SQL INSERT statement
    - RAG system answer (if answerable)
    - Analysis and reasoning
    """
    try:
        result = await service.classify_question(
            question_text=question,
            lead_id=lead_id,
            franchisor_id=franchisor_id,
            context=None
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=result.get("error_code", 500),
                detail=result.get("message", {}).get("description", "Classification failed")
            )
        
        return QuestionClassificationResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred during test classification: {str(e)}"
        )
