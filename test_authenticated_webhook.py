#!/usr/bin/env python3
"""
Test Authenticated <PERSON><PERSON>ity SMS Webhook
Tests the webhook with proper authentication using the provided access token
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# Configuration
ACCESS_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.QnbbtSJMZbA9C9K2MI0DzRv1TKXpQ82mHROa25BhG5U"
WEBHOOK_URL = "http://localhost:8000/api/webhooks/webhooks/kudosity"

def create_test_payload(message: str, phone_number: str = None) -> dict:
    """Create a test payload with unique phone number to avoid database conflicts"""
    if phone_number is None:
        # Generate unique phone number based on message hash
        phone_number = f"61430{hash(message) % 100000:05d}"
    
    return {
        "event_type": "SMS_INBOUND",
        "timestamp": "2025-07-02T14:39:54Z",
        "webhook_id": "1b39ae29-f723-4426-9d35-efa763959c1b",
        "webhook_name": "openxcell_webbook",
        "mo": {
            "type": "SMS",
            "id": f"test-{hash(message)}",
            "sender": phone_number,
            "recipient": "61430250079",
            "message": message,
            "last_message": {
                "id": f"last-{hash(message)}",
                "type": "SMS",
                "sender": "61430250079",
                "recipient": "61430250079",
                "message": "Previous message",
                "message_ref": f"ref-{hash(message)}"
            }
        }
    }

async def test_authenticated_webhook():
    """Test the Kudosity SMS webhook with authentication"""
    async with httpx.AsyncClient() as client:
        print("🧪 Testing Authenticated Kudosity SMS Webhook")
        print("=" * 60)
        
        # Test cases with expected classifications
        test_cases = [
            {
                "message": "What's the ROI of this franchise?",
                "expected_classification": "answerable",
                "description": "Valid franchise question"
            },
            {
                "message": "What are the training requirements?",
                "expected_classification": "answerable", 
                "description": "Training information question"
            },
            {
                "message": "Help",
                "expected_classification": "escalation",
                "description": "Vague help request"
            },
            {
                "message": "I need immediate assistance",
                "expected_classification": "escalation",
                "description": "Escalation keywords"
            },
            {
                "message": "What's your GST number?",
                "expected_classification": "escalation",
                "description": "Requires specific business info"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 Test Case {i}: {test_case['description']}")
            print(f"📱 Message: {test_case['message']}")
            print(f"🎯 Expected: {test_case['expected_classification']}")
            
            # Create unique payload for each test
            payload = create_test_payload(test_case['message'])
            
            try:
                response = await client.post(
                    WEBHOOK_URL,
                    json=payload,
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {ACCESS_TOKEN}"
                    },
                    timeout=30.0
                )
                
                print(f"📊 Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    response_data = response.json()
                    print(f"✅ Success: {response_data.get('success', False)}")
                    
                    # Check classification field
                    classification = response_data.get('data', {}).get('classification', 'unknown')
                    classification_reason = response_data.get('data', {}).get('classification_reason', 'No reason provided')
                    ai_response = response_data.get('data', {}).get('ai_response', 'No response')
                    
                    print(f"🏷️  Classification: {classification}")
                    print(f"📋 Reason: {classification_reason}")
                    print(f"🤖 AI Response: {ai_response[:100]}...")
                    
                    # Verify classification
                    if classification == test_case['expected_classification']:
                        print(f"✅ PASS: Classification matches expected '{test_case['expected_classification']}'")
                    else:
                        print(f"❌ FAIL: Expected '{test_case['expected_classification']}' but got '{classification}'")
                        
                else:
                    print(f"❌ Error: {response.status_code}")
                    print(f"Response: {response.text}")
                    
            except Exception as e:
                print(f"❌ Exception: {str(e)}")
            
            print("-" * 40)

def analyze_classification_response(question: str, classification: str, expected: str) -> str:
    """Analyze the classification response"""
    if classification == expected:
        return f"✅ CORRECT: '{question}' correctly classified as '{expected}'"
    else:
        return f"❌ INCORRECT: '{question}' classified as '{classification}' but expected '{expected}'"

async def test_health_with_auth():
    """Test the health endpoint with authentication"""
    async with httpx.AsyncClient() as client:
        print("\n🏥 Testing Health Endpoint with Authentication")
        print("=" * 50)
        
        try:
            response = await client.get(
                "http://localhost:8000/api/webhooks/webhooks/health",
                headers={"Authorization": f"Bearer {ACCESS_TOKEN}"},
                timeout=10.0
            )
            
            print(f"📊 Status Code: {response.status_code}")
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Health Status: {health_data.get('status', 'unknown')}")
                print(f"🤖 QnA Available: {health_data.get('qna_available', False)}")
                print(f"⏰ Timestamp: {health_data.get('timestamp', 'unknown')}")
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {str(e)}")

async def test_question_classification_with_auth():
    """Test the question classification endpoint with authentication"""
    async with httpx.AsyncClient() as client:
        print("\n🎯 Testing Question Classification Endpoint with Authentication")
        print("=" * 60)
        
        test_questions = [
            {
                "question": "What's the ROI of this franchise?",
                "expected": "answerable"
            },
            {
                "question": "Help me please",
                "expected": "escalation"
            }
        ]
        
        for i, test in enumerate(test_questions, 1):
            print(f"\n📝 Test {i}: {test['question']}")
            
            try:
                response = await client.post(
                    "http://localhost:8000/api/v1/question-classification/classify",
                    json={
                        "question_text": test['question'],
                        "lead_id": None,
                        "franchisor_id": None
                    },
                    headers={
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {ACCESS_TOKEN}"
                    },
                    timeout=30.0
                )
                
                print(f"📊 Status Code: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    classification = result.get('data', {}).get('classification', 'unknown')
                    print(f"🏷️  Classification: {classification}")
                    print(f"📋 Expected: {test['expected']}")
                    
                    if classification == test['expected']:
                        print("✅ PASS")
                    else:
                        print("❌ FAIL")
                else:
                    print(f"❌ Error: {response.status_code}")
                    print(f"Response: {response.text}")
                    
            except Exception as e:
                print(f"❌ Exception: {str(e)}")

async def main():
    """Run all tests"""
    print("🚀 Starting Authenticated Webhook Tests")
    print("=" * 50)
    
    await test_health_with_auth()
    await test_authenticated_webhook()
    await test_question_classification_with_auth()
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    asyncio.run(main()) 